from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame,
                            QTextBrowser)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QPainter, QTextDocument, QPixmap, QBrush, QPen, QIcon, QLinearGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Invoice, InvoiceItem, Client
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency,
                    generate_invoice_number, format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)

class InvoiceItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر فاتورة"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.item:
            self.setWindowTitle("تعديل عنصر الفاتورة")
        else:
            self.setWindowTitle("إضافة عنصر جديد للفاتورة")

        self.setMinimumWidth(400)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل الوصف
        self.description_edit = QLineEdit()
        self.description_edit.setStyleSheet(UnifiedStyles.get_input_style())
        if self.item:
            self.description_edit.setText(self.item.description)
        form_layout.addRow("الوصف:", self.description_edit)

        # حقل الكمية
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(1, 10000)  # الحد الأدنى 1 بدلاً من 0.01
        self.quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.quantity_edit.setSingleStep(1)
        self.quantity_edit.setValue(1)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity)
        self.quantity_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_edit)

        # حقل سعر الوحدة
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setRange(1, 1000000)  # الحد الأدنى 1 بدلاً من 0.01
        self.unit_price_edit.setDecimals(0)  # بدون كسور عشرية
        self.unit_price_edit.setSingleStep(10)
        if self.item:
            self.unit_price_edit.setValue(self.item.unit_price)
        self.unit_price_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # حقل السعر الإجمالي
        self.total_price_label = QLabel("0.00")
        form_layout.addRow("السعر الإجمالي:", self.total_price_label)

        # حساب السعر الإجمالي الأولي
        self.calculate_total()

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        button_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        if hasattr(self.parent(), 'style_advanced_button'):
            self.parent().style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        if hasattr(self.parent(), 'style_advanced_button'):
            self.parent().style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)

        # تجميع التخطيط النهائي
        main_layout = QVBoxLayout()
        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def calculate_total(self):
        """حساب السعر الإجمالي للعنصر"""
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price
        self.total_price_label.setText(format_currency(total_price))

    def get_data(self):
        """الحصول على بيانات عنصر الفاتورة من النموذج"""
        description = self.description_edit.text().strip()
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price

        # التحقق من صحة البيانات
        if not description:
            show_error_message("خطأ", "يجب إدخال وصف العنصر")
            return None

        if quantity <= 0:
            show_error_message("خطأ", "يجب أن تكون الكمية أكبر من صفر")
            return None

        if unit_price <= 0:
            show_error_message("خطأ", "يجب أن يكون سعر الوحدة أكبر من صفر")
            return None

        return {
            'description': description,
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }


class InvoiceDialog(QDialog):
    """نافذة إضافة/تعديل فاتورة - تصميم جديد نظيف"""

    def __init__(self, parent=None, invoice=None, session=None):
        super().__init__(parent)
        self.invoice = invoice
        self.session = session
        self.items = []

        # نسخ عناصر الفاتورة الموجودة
        if self.invoice and self.invoice.items:
            for item in self.invoice.items:
                self.items.append({
                    'id': item.id,
                    'description': item.description,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                })

        self.setup_ui()
        self.update_total()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        # إعداد النافذة مطابق للعملاء والموردين
        title = "تعديل فاتورة" if self.invoice else "إضافة فاتورة جديدة"
        self.setWindowTitle(f"📄 {title} - نظام إدارة الفواتير المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        self.setModal(True)
        self.resize(750, 500)  # تقليل الارتفاع أكثر بعد إزالة العنوان

        # خلفية النافذة مطابقة للعملاء والموردين
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للفواتير
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            gradient = QLinearGradient(0, 0, 48, 48)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز الفواتير
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📄")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان مطابق للعملاء والموردين"""
        try:
            import platform
            if platform.system() == "Windows":
                import ctypes
                from ctypes import wintypes

                # الحصول على handle النافذة
                hwnd = int(self.winId())

                # تعيين لون شريط العنوان
                DWMWA_CAPTION_COLOR = 35
                color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(wintypes.DWORD(color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

                # تعيين لون النص في شريط العنوان
                DWMWA_TEXT_COLOR = 36
                text_color = 0x00FFFFFF  # أبيض

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_TEXT_COLOR,
                    ctypes.byref(wintypes.DWORD(text_color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

        # التخطيط الرئيسي مع مسافات مقللة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات بين العناصر

        # نموذج البيانات الأساسية مع مسافات مقللة
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(10)  # تقليل المسافات بين الحقول
        form_layout.setContentsMargins(10, 10, 10, 10)  # تقليل الهوامش

        # دالة لإنشاء تسميات مصممة مضغوطة
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 6px 10px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 100px;
                    max-width: 100px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)
        else:
            self.invoice_number_edit.setText(generate_invoice_number(self.session))
        self.style_input(self.invoice_number_edit)
        form_layout.addRow(create_styled_label("رقم الفاتورة", "📄", True), self.invoice_number_edit)

        # العميل
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        if self.invoice and self.invoice.client_id:
            index = self.client_combo.findData(self.invoice.client_id)
            if index >= 0:
                self.client_combo.setCurrentIndex(index)

        self.style_combo(self.client_combo)
        form_layout.addRow(create_styled_label("العميل", "👤", True), self.client_combo)

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        if self.invoice and self.invoice.date:
            self.date_edit.setDate(datetime_to_qdate(self.invoice.date))
        self.style_input(self.date_edit)
        form_layout.addRow(create_styled_label("التاريخ", "📅", True), self.date_edit)

        # تاريخ الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(datetime_to_qdate(self.invoice.due_date))
        self.style_input(self.due_date_edit)
        form_layout.addRow(create_styled_label("تاريخ الاستحقاق", "⏰"), self.due_date_edit)

        # الحالة
        self.status_combo = QComboBox()
        statuses = [("pending", "معلقة"), ("paid", "مدفوعة"), ("partially_paid", "مدفوعة جزئياً"), ("cancelled", "ملغية")]
        for status_code, status_name in statuses:
            self.status_combo.addItem(status_name, status_code)

        if self.invoice and self.invoice.status:
            index = self.status_combo.findData(self.invoice.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

        self.style_combo(self.status_combo)
        form_layout.addRow(create_styled_label("الحالة", "📊"), self.status_combo)

        # المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setRange(0, 1000000)
        self.paid_amount_edit.setDecimals(2)
        self.paid_amount_edit.setSingleStep(100)
        if self.invoice:
            self.paid_amount_edit.setValue(self.invoice.paid_amount)
        self.style_input(self.paid_amount_edit)
        form_layout.addRow(create_styled_label("المبلغ المدفوع", "💰"), self.paid_amount_edit)

        layout.addWidget(form_widget)

        # جدول العناصر مضغوط
        items_widget = QWidget()
        items_layout = QVBoxLayout(items_widget)
        items_layout.setSpacing(8)  # تقليل المسافات
        items_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش

        # عنوان العناصر مضغوط
        items_title = QLabel("📋 عناصر الفاتورة")
        items_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                margin: 5px 0;
            }
        """)
        items_layout.addWidget(items_title)

        # الجدول مضغوط
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["الوصف", "الكمية", "سعر الوحدة", "الإجمالي", "حذف"])

        # تصميم الجدول مطابق للعملاء والموردين
        self.items_table.setStyleSheet("""
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
                border: 1px solid rgba(96, 165, 250, 0.3);
                border-radius: 6px;
                gridline-color: rgba(96, 165, 250, 0.2);
                selection-background-color: rgba(96, 165, 250, 0.3);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 8px 12px !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                font-size: 12px !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7) !important;
                text-align: center !important;
                height: 35px !important;
                letter-spacing: 1px !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-1px) scale(1.01) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 15px rgba(134, 158, 234, 0.3) !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.99) !important;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            }
        """)

        # ضبط ارتفاع الصفوف
        self.items_table.verticalHeader().setDefaultSectionSize(35)
        self.items_table.setMaximumHeight(200)  # تحديد ارتفاع أقصى للجدول

        self.items_table.horizontalHeader().setStretchLastSection(True)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        items_layout.addWidget(self.items_table)

        # أزرار العناصر مطابقة للنمط الموحد
        items_buttons = QHBoxLayout()
        items_buttons.setSpacing(15)

        add_item_btn = QPushButton("➕ إضافة عنصر")
        add_item_btn.clicked.connect(self.add_item)
        self.style_button(add_item_btn, "emerald")
        items_buttons.addWidget(add_item_btn)

        edit_item_btn = QPushButton("✏️ تعديل عنصر")
        edit_item_btn.clicked.connect(self.edit_item)
        self.style_button(edit_item_btn, "primary")
        items_buttons.addWidget(edit_item_btn)

        delete_item_btn = QPushButton("🗑️ حذف عنصر")
        delete_item_btn.clicked.connect(self.delete_selected_item)
        self.style_button(delete_item_btn, "danger")
        items_buttons.addWidget(delete_item_btn)

        # إضافة مساحة مرنة
        items_buttons.addStretch()

        items_layout.addLayout(items_buttons)

        # إجمالي الفاتورة مطابق للنمط الموحد
        self.total_label = QLabel("الإجمالي: 0.00 جنيه")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(16, 185, 129, 0.8),
                    stop:0.3 rgba(34, 197, 94, 0.7),
                    stop:0.7 rgba(59, 130, 246, 0.7),
                    stop:1 rgba(139, 92, 246, 0.8));
                border: 2px solid rgba(16, 185, 129, 0.9);
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(16, 185, 129, 0.4);
                margin: 10px 0;
            }
        """)
        items_layout.addWidget(self.total_label)

        layout.addWidget(items_widget)

        # الملاحظات مضغوطة
        notes_widget = QWidget()
        notes_layout = QVBoxLayout(notes_widget)
        notes_layout.setSpacing(5)
        notes_layout.setContentsMargins(5, 5, 5, 5)

        notes_title = QLabel("📝 ملاحظات")
        notes_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                margin: 5px 0;
            }
        """)
        notes_layout.addWidget(notes_title)

        self.notes_edit = QTextEdit()
        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)
        # تصميم مضغوط للملاحظات
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: normal;
                color: #1f2937;
                min-height: 50px;
                max-height: 80px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
            }
            QTextEdit:focus {
                border: 3px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
            }
        """)
        notes_layout.addWidget(self.notes_edit)

        layout.addWidget(notes_widget)

        # أزرار التحكم مضغوطة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(10, 5, 10, 5)

        # إضافة مساحة مرنة في البداية
        buttons_layout.addStretch()

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setMinimumSize(120, 45)
        self.style_button(cancel_btn, "danger")
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("💾 حفظ الفاتورة")
        save_btn.clicked.connect(self.accept)
        save_btn.setMinimumSize(150, 45)
        self.style_button(save_btn, "emerald")
        buttons_layout.addWidget(save_btn)

        # إضافة مساحة مرنة في النهاية
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # تحديث الجدول
        self.update_items_table()

    def style_input(self, widget):
        """تطبيق تصميم مضغوط على حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: normal;
                color: #1f2937;
                min-height: 20px;
                min-width: 280px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
            }
            QLineEdit:focus, QDateEdit:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border: 3px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
            }
        """)

    def style_combo(self, combo):
        """تطبيق تصميم موحد على القوائم المنسدلة مطابق للنمط الجديد (بدون إطارات)"""
        combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)

    def style_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء والموردين"""
        try:
            # ألوان مطابقة تماماً للعملاء والموردين
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مطابق للعملاء والموردين
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.5px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.95);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               inset 0 2px 4px rgba(0, 0, 0, 0.4),
                               0 2px 8px {color_scheme['shadow']};
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم بسيط كبديل
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1d4ed8;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #60a5fa;
                }}
                QPushButton:pressed {{
                    background-color: #1d4ed8;
                }}
            """)

    def update_items_table(self):
        """تحديث جدول العناصر مع تصميم محسن"""
        self.items_table.setRowCount(len(self.items))

        for row, item in enumerate(self.items):
            # الوصف
            desc_item = QTableWidgetItem(item.get('description', ''))
            desc_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 0, desc_item)

            # الكمية
            qty_item = QTableWidgetItem(str(item.get('quantity', 0)))
            qty_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, qty_item)

            # سعر الوحدة
            price_item = QTableWidgetItem(f"{item.get('unit_price', 0):.2f} جنيه")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item.get('total_price', 0):.2f} جنيه")
            total_item.setTextAlignment(Qt.AlignCenter)
            # تلوين الإجمالي بالأخضر
            total_item.setForeground(QColor(16, 185, 129))
            self.items_table.setItem(row, 3, total_item)

            # زر حذف مطابق للنمط الجديد
            delete_btn = QPushButton("🗑️")
            delete_btn.setToolTip("حذف هذا العنصر")
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_item(r))
            delete_btn.setMinimumSize(30, 30)
            self.style_button(delete_btn, "danger")
            self.items_table.setCellWidget(row, 4, delete_btn)

        # ضبط عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # الوصف
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # حذف

    def update_total(self):
        """تحديث المجموع الإجمالي"""
        total = sum(item.get('total_price', 0) for item in self.items)
        self.total_label.setText(f"الإجمالي: {total:.2f} جنيه")

    def add_item(self):
        """إضافة عنصر جديد"""
        dialog = InvoiceItemDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                self.items.append(data)
                self.update_items_table()
                self.update_total()

    def edit_item(self):
        """تعديل عنصر محدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0 and current_row < len(self.items):
            # إنشاء كائن مؤقت للتعديل
            temp_item = type('obj', (object,), self.items[current_row])
            dialog = InvoiceItemDialog(self, temp_item)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    self.items[current_row] = data
                    self.update_items_table()
                    self.update_total()

    def delete_item(self, row):
        """حذف عنصر"""
        if 0 <= row < len(self.items):
            del self.items[row]
            self.update_items_table()
            self.update_total()

    def delete_selected_item(self):
        """حذف العنصر المحدد من الجدول"""
        current_row = self.items_table.currentRow()
        if current_row >= 0 and current_row < len(self.items):
            # تأكيد الحذف
            from utils.helpers import show_confirmation_message
            if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟"):
                self.delete_item(current_row)
        else:
            from utils.helpers import show_error_message
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")

    def get_data(self):
        """الحصول على بيانات الفاتورة"""
        try:
            invoice_number = self.invoice_number_edit.text().strip()
            client_id = self.client_combo.currentData()
            date = qdate_to_datetime(self.date_edit.date())
            due_date = qdate_to_datetime(self.due_date_edit.date())
            status = self.status_combo.currentData()
            paid_amount = self.paid_amount_edit.value()
            notes = self.notes_edit.toPlainText().strip()

            # حساب المجموع الإجمالي
            total_amount = sum(item.get('total_price', 0) for item in self.items)

            # التحقق من صحة البيانات
            if not invoice_number:
                show_error_message("خطأ", "يجب إدخال رقم الفاتورة")
                return None

            if not client_id:
                show_error_message("خطأ", "يجب اختيار عميل")
                return None

            if not self.items:
                show_error_message("خطأ", "يجب إضافة عنصر واحد على الأقل")
                return None

            return {
                'invoice_number': invoice_number,
                'client_id': client_id,
                'date': date,
                'due_date': due_date,
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'status': status,
                'notes': notes,
                'items': self.items
            }
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ: {str(e)}")
            return None


class InvoicesWidget(QWidget):
    """واجهة إدارة الفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()



    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين
        title_label = QLabel("📋 إدارة الفواتير المتطورة - نظام شامل ومتقدم لإدارة الفواتير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث برقم الفاتورة، العميل أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_invoices)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_invoices)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الفواتير المتطور والمحسن
        self.create_advanced_invoices_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.invoices_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار النظيفة والمرتبة

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة فاتورة")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_invoice)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_invoice)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_invoice)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')
        self.view_button.clicked.connect(self.view_invoice)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إضافة دفعة
        self.add_payment_button = QPushButton("💰 إضافة دفعة")
        self.style_advanced_button(self.add_payment_button, 'orange')
        self.add_payment_button.clicked.connect(self.add_payment)
        self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        self.export_button.setMenu(export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي الفواتير مطور ليتشابه مع الأزرار مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي الفواتير: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(550, self.refresh_data)

    def create_advanced_invoices_table(self):
        """إنشاء جدول الفواتير المتطور والنظيف"""
        # إنشاء الجدول
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)

        # عناوين الأعمدة مع الأيقونات
        headers = [
            "🔢 ID",
            "📋 رقم الفاتورة",
            "🧑‍💼 العميل",
            "📆 تاريخ الإنشاء",
            "⏳ موعد الاستحقاق",
            "💎 إجمالي المبلغ",
            "💵 المبلغ المسدد",
            "🎯 حالة الدفع"
        ]
        self.invoices_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.SingleSelection)
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setAlternatingRowColors(False)
        self.invoices_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة
        self.invoices_table.verticalHeader().setDefaultSectionSize(50)
        self.invoices_table.verticalHeader().setVisible(False)

        header = self.invoices_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.invoices_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.invoices_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم والتفاعل
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول"""
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية مبسطة للجدول"""
        try:
            # علامة مائية مبسطة بدون تعقيد في الرسم
            pass  # تم تعطيل العلامة المائية المعقدة مؤقتاً
        except Exception as e:
            print(f"تحذير: فشل في إضافة العلامة المائية: {e}")

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول"""
        self.invoices_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.invoices_table.cellDoubleClicked.connect(self.edit_invoice)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.invoices_table.itemAt(event.pos())
            if item is None:
                self.invoices_table.clearSelection()
            QTableWidget.mousePressEvent(self.invoices_table, event)

        self.invoices_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.invoices_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.invoices_table, event)

        self.invoices_table.wheelEvent = wheelEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        selected_items = self.invoices_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.view_button.setEnabled(has_selection)
        self.add_payment_button.setEnabled(has_selection)


    def get_selected_invoice_id(self):
        """استخراج معرف الفاتورة المحددة من الجدول"""
        try:
            selected_row = self.invoices_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار فاتورة من القائمة"

            if not self.invoices_table.item(selected_row, 0):
                return None, "الرجاء اختيار فاتورة صالحة من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.invoices_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم الفاتورة"

            return int(numbers[0]), None
        except Exception as e:
            return None, f"خطأ في استخراج معرف الفاتورة: {str(e)}"



    def refresh_data(self):
        """تحديث بيانات الفواتير في الجدول"""
        try:
            # الحصول على جميع الفواتير من قاعدة البيانات
            invoices = self.session.query(Invoice).all()
            self.populate_table(invoices)
            self.update_summary(invoices)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def get_invoice_status(self, status):
        """تحديد حالة الفاتورة مطابق للعملاء"""
        status_map = {
            'pending': '🟡 قيد الانتظار',
            'paid': '🟢 مدفوعة بالكامل',
            'partially_paid': '🟢 مدفوعة جزئياً',
            'cancelled': '🔴 ملغاة'
        }
        return status_map.get(status, '🟡 غير محدد')

    def populate_table(self, invoices):
        """ملء جدول الفواتير بالبيانات"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.invoices_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.invoices_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن
            for row, invoice in enumerate(invoices):
                try:
                    self.invoices_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة ثابتة مطابق للعملاء
                    # الفواتير دائماً تستخدم أيقونة 🔢 لأنها لا تحتوي على رصيد
                    id_item = QTableWidgetItem(f"🔢 {invoice.id}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    self.invoices_table.setItem(row, 0, id_item)

                    # دالة مساعدة لإنشاء العناصر مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    invoice_num = invoice.invoice_number or f"INV-{str(invoice.id).zfill(6)}"
                    client_name = invoice.client.name if invoice.client else None

                    self.invoices_table.setItem(row, 1, create_item("📋", invoice_num))
                    self.invoices_table.setItem(row, 2, create_item("🧑‍💼", client_name))

                    # التواريخ مطابق للعملاء
                    date_text = invoice.date.strftime("%Y-%m-%d") if invoice.date else None
                    due_date_text = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else None

                    self.invoices_table.setItem(row, 3, create_item("📆", date_text))
                    self.invoices_table.setItem(row, 4, create_item("⏳", due_date_text))

                    # المبالغ مطابق للعملاء
                    from utils import format_currency
                    total_text = format_currency(invoice.total_amount) if invoice.total_amount else None
                    paid_text = format_currency(invoice.paid_amount) if invoice.paid_amount else None

                    self.invoices_table.setItem(row, 5, create_item("💰", total_text))
                    self.invoices_table.setItem(row, 6, create_item("💵", paid_text))

                    # 8. حالة الدفع مطابق للعملاء
                    status_item = QTableWidgetItem(self.get_invoice_status(invoice.status))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 7, status_item)
                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تمكين تحديث الجدول
            self.invoices_table.setUpdatesEnabled(True)
        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.invoices_table.setUpdatesEnabled(True)
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول الفواتير: {str(e)}")

    def update_summary(self, invoices):
        """تحديث ملخص الفواتير"""
        try:
            # حساب المبالغ
            total = sum(invoice.total_amount or 0 for invoice in invoices)
            paid = sum(invoice.paid_amount or 0 for invoice in invoices)
            balance = total - paid

            # تحديث النصوص
            self.total_label.setText(f"إجمالي الفواتير: {format_currency(total)} | المدفوعات: {format_currency(paid)} | المستحقات: {format_currency(balance)}")
        except Exception as e:
            # في حالة حدوث خطأ، عرض قيم افتراضية
            self.total_label.setText("إجمالي الفواتير: 0.00 | المدفوعات: 0.00 | المستحقات: 0.00")
            print(f"خطأ في تحديث ملخص الفواتير: {str(e)}")

    def filter_invoices(self):
        """تصفية الفواتير بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Invoice).join(Client, Invoice.client_id == Client.id, isouter=True)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Invoice.invoice_number.like(f"%{search_text}%") |
                    Client.name.like(f"%{search_text}%") |
                    Invoice.notes.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status:
                query = query.filter(Invoice.status == status)

            # تنفيذ الاستعلام
            invoices = query.all()

            # تحديث الجدول والملخص
            self.populate_table(invoices)
            self.update_summary(invoices)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصفية الفواتير: {str(e)}")

    def add_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            dialog = InvoiceDialog(self, session=self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # إنشاء فاتورة جديدة في قاعدة البيانات
                    invoice = Invoice(**data)
                    self.session.add(invoice)
                    self.session.flush()  # للحصول على معرف الفاتورة

                    # إضافة عناصر الفاتورة
                    for item_data in items_data:
                        if all(key in item_data for key in ['description', 'quantity', 'unit_price', 'total_price']):
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()

                    show_info_message("تم", "تم إنشاء الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")

    def edit_invoice(self):
        """تعديل بيانات فاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            dialog = InvoiceDialog(self, invoice, self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # تحديث بيانات الفاتورة
                    for key, value in data.items():
                        setattr(invoice, key, value)

                    # حذف جميع عناصر الفاتورة الحالية
                    for item in invoice.items:
                        self.session.delete(item)

                    # إضافة عناصر الفاتورة الجديدة
                    for item_data in items_data:
                        if all(key in item_data for key in ['description', 'quantity', 'unit_price', 'total_price']):
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()
                    show_info_message("تم", "تم تحديث بيانات الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء تعديل الفاتورة: {str(e)}")

    def delete_invoice(self):
        """حذف فاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # التحقق من وجود إيرادات مرتبطة بالفاتورة
            if invoice.revenues:
                show_error_message(
                    "خطأ",
                    f"لا يمكن حذف الفاتورة لأنها مرتبطة بـ {len(invoice.revenues)} إيراد. قم بحذف الإيرادات أولاً."
                )
                return

            # طلب تأكيد الحذف
            if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الفاتورة {invoice.invoice_number}؟"):
                # حذف جميع عناصر الفاتورة
                for item in invoice.items:
                    self.session.delete(item)

                # حذف الفاتورة
                self.session.delete(invoice)
                self.session.commit()
                show_info_message("تم", "تم حذف الفاتورة بنجاح")
                self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}")

    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # إنشاء نافذة لعرض تفاصيل الفاتورة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"تفاصيل الفاتورة - {invoice.invoice_number}")
            dialog.setMinimumSize(800, 600)

            layout = QVBoxLayout()

            # إنشاء مستعرض نصي لعرض تفاصيل الفاتورة
            text_browser = QTextBrowser()
            text_browser.setOpenExternalLinks(False)
            html_content = self.generate_invoice_html(invoice)
            text_browser.setHtml(html_content)
            layout.addWidget(text_browser)

            # أزرار الإجراءات
            button_layout = QHBoxLayout()
            button_layout.setSpacing(10)

            # زر تصدير PDF
            export_pdf_button = StyledButton("📤 تصدير PDF", "warning", "normal")
            export_pdf_button.clicked.connect(lambda: self.export_invoice_to_pdf(invoice, dialog))

            # زر طباعة
            print_button = StyledButton("🖨️ طباعة", "info", "normal")
            print_button.clicked.connect(lambda: self.print_invoice_from_dialog(invoice))

            # زر إغلاق
            close_button = StyledButton("❌ إغلاق", "secondary", "normal")
            close_button.clicked.connect(dialog.accept)

            button_layout.addStretch()
            button_layout.addWidget(export_pdf_button.button)
            button_layout.addWidget(print_button.button)
            button_layout.addWidget(close_button.button)

            layout.addLayout(button_layout)
            dialog.setLayout(layout)

            dialog.exec_()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض الفاتورة: {str(e)}")

    def generate_invoice_html(self, invoice):
        """إنشاء محتوى HTML للفاتورة"""
        try:
            # ترجمة حالة الفاتورة
            status_map = {
                'pending': 'قيد الانتظار',
                'paid': 'مدفوعة',
                'partially_paid': 'مدفوعة جزئيًا',
                'cancelled': 'ملغاة'
            }
            status_text = status_map.get(invoice.status, invoice.status or "")

            # تنسيق التواريخ
            date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
            due_date_str = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else ""

            # إنشاء جدول عناصر الفاتورة
            items_html = """
            <table border="1" cellpadding="5" cellspacing="0" width="100%">
                <tr style="background-color: #f2f2f2;">
                    <th>السعر الإجمالي</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية</th>
                    <th>الوصف</th>
                </tr>
            """

            # إضافة عناصر الفاتورة
            if invoice.items:
                for item in invoice.items:
                    items_html += f"""
                    <tr>
                        <td>{format_currency(item.total_price)}</td>
                        <td>{format_currency(item.unit_price)}</td>
                        <td>{format_quantity(item.quantity)}</td>
                        <td>{item.description}</td>
                    </tr>
                    """
            else:
                items_html += """
                <tr>
                    <td colspan="4" style="text-align: center;">لا توجد عناصر</td>
                </tr>
                """

            items_html += "</table>"

            # حساب الرصيد المتبقي
            total_amount = invoice.total_amount or 0
            paid_amount = invoice.paid_amount or 0
            balance = total_amount - paid_amount

            # بيانات العميل
            client_name = invoice.client.name if invoice.client else ""
            client_address = invoice.client.address if invoice.client else ""
            client_phone = invoice.client.phone if invoice.client else ""

            # إنشاء محتوى HTML كامل للفاتورة
            html = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; }}
                    .invoice-header {{ text-align: center; margin-bottom: 20px; }}
                    .invoice-details {{ margin-bottom: 20px; }}
                    .invoice-details table {{ width: 100%; }}
                    .invoice-items {{ margin-bottom: 20px; }}
                    .invoice-summary {{ text-align: left; margin-top: 20px; }}
                    .invoice-notes {{ margin-top: 30px; border-top: 1px solid #ccc; padding-top: 10px; }}
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>فاتورة</h1>
                    <h2>{invoice.invoice_number}</h2>
                </div>

                <div class="invoice-details">
                    <table width="100%" style="border-collapse: collapse;">
                        <tr>
                            <td width="33%" style="text-align: right;"><strong>العميل:</strong> {client_name}</td>
                            <td width="34%" style="text-align: center;"><strong>العنوان:</strong> {client_address}</td>
                            <td width="33%" style="text-align: left;"><strong>التاريخ:</strong> {date_str}</td>
                        </tr>
                        <tr>
                            <td style="text-align: right;"><strong>الهاتف:</strong> {client_phone}</td>
                            <td style="text-align: center;"><strong>الحالة:</strong> {status_text}</td>
                            <td style="text-align: left;"><strong>تاريخ الدفع القادم:</strong> {due_date_str}</td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-items">
                    <h3>عناصر الفاتورة</h3>
                    {items_html}
                </div>

                <div class="invoice-summary">
                    <p><strong>المبلغ الإجمالي:</strong> {format_currency(total_amount)}</p>
                    <p><strong>المبلغ المدفوع:</strong> {format_currency(paid_amount)}</p>
                    <p><strong>الرصيد المتبقي:</strong> {format_currency(balance)}</p>
                </div>

                <div class="invoice-notes">
                    <h3>ملاحظات</h3>
                    <p>{invoice.notes or ""}</p>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في عرض الفاتورة</h1>
                <p>حدث خطأ أثناء محاولة عرض تفاصيل الفاتورة.</p>
                <p>رسالة الخطأ: {str(e)}</p>
            </body>
            </html>
            """

    def add_payment(self):
        """إضافة دفعة للفاتورة المحددة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # التحقق من حالة الفاتورة
            if invoice.status in ['cancelled', 'paid']:
                show_error_message("خطأ", "لا يمكن إضافة دفعة لهذه الفاتورة")
                return

            # حساب الرصيد المتبقي
            balance = invoice.total_amount - invoice.paid_amount
            if balance <= 0:
                show_error_message("خطأ", "لا يوجد رصيد متبقي للفاتورة")
                return

            # إنشاء نافذة حوار لإضافة دفعة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"إضافة دفعة للفاتورة {invoice.invoice_number}")
            dialog.setMinimumWidth(400)

            layout = QVBoxLayout()
            form_layout = QFormLayout()

            # عرض معلومات الفاتورة
            invoice_label = QLabel(f"{invoice.invoice_number} - {invoice.client.name if invoice.client else ''}")
            form_layout.addRow("الفاتورة:", invoice_label)

            total_label = QLabel(format_currency(invoice.total_amount))
            form_layout.addRow("المبلغ الإجمالي:", total_label)

            paid_label = QLabel(format_currency(invoice.paid_amount))
            form_layout.addRow("المبلغ المدفوع:", paid_label)

            balance_label = QLabel(format_currency(balance))
            form_layout.addRow("الرصيد المتبقي:", balance_label)

            # حقل مبلغ الدفعة
            amount_edit = QDoubleSpinBox()
            amount_edit.setRange(1, balance)
            amount_edit.setDecimals(0)
            amount_edit.setSingleStep(100)
            amount_edit.setValue(balance)
            form_layout.addRow("مبلغ الدفعة:", amount_edit)

            # حقل تاريخ الدفع
            date_edit = QDateEdit()
            date_edit.setCalendarPopup(True)
            date_edit.setDate(QDate.currentDate())
            form_layout.addRow("تاريخ الدفع:", date_edit)

            # حقل الملاحظات
            notes_edit = QTextEdit()
            notes_edit.setPlaceholderText("ملاحظات حول الدفعة...")
            form_layout.addRow("ملاحظات:", notes_edit)

            layout.addLayout(form_layout)

            # أزرار الحفظ والإلغاء
            button_layout = QHBoxLayout()

            save_button = StyledButton("💾 حفظ", "success", "normal")
            save_button.clicked.connect(dialog.accept)

            cancel_button = StyledButton("❌ إلغاء", "secondary", "normal")
            cancel_button.clicked.connect(dialog.reject)

            button_layout.addWidget(save_button.button)
            button_layout.addWidget(cancel_button.button)

            layout.addLayout(button_layout)
            dialog.setLayout(layout)

            if dialog.exec_() == QDialog.Accepted:
                amount = amount_edit.value()
                payment_date = qdate_to_datetime(date_edit.date())
                notes = notes_edit.toPlainText().strip()

                if amount <= 0 or amount > balance:
                    show_error_message("خطأ", "مبلغ الدفعة غير صحيح")
                    return

                # إنشاء إيراد جديد للدفعة
                from database import Revenue
                revenue = Revenue(
                    title=f"دفعة للفاتورة {invoice.invoice_number}",
                    amount=amount,
                    date=payment_date,
                    category="مبيعات",
                    invoice_id=invoice.id,
                    notes=notes
                )

                # إضافة الإيراد إلى قاعدة البيانات
                self.session.add(revenue)

                # تحديث المبلغ المدفوع وحالة الفاتورة
                invoice.paid_amount += amount
                invoice.status = 'paid' if invoice.paid_amount >= invoice.total_amount else 'partially_paid'

                # حفظ التغييرات
                self.session.commit()
                show_info_message("تم", "تم إضافة الدفعة بنجاح")
                self.refresh_data()
        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة الدفعة: {str(e)}")

    def export_invoice_to_pdf(self, invoice, parent_dialog=None):
        """تصدير الفاتورة إلى ملف PDF"""
        try:
            # اختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                parent_dialog or self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{invoice.invoice_number}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return  # المستخدم ألغى العملية

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()
            html_content = self.generate_invoice_html(invoice)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير الفاتورة بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير الفاتورة إلى PDF: {str(e)}")

    def print_invoice_from_dialog(self, invoice):
        """طباعة الفاتورة من نافذة التفاصيل"""
        try:
            # إنشاء مستند للطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            # إظهار مربع حوار الطباعة
            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QDialog.Accepted:
                # إنشاء مستند نصي
                document = QTextDocument()
                html_content = self.generate_invoice_html(invoice)
                document.setHtml(html_content)
                # طباعة المستند
                document.print_(printer)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء طباعة الفاتورة: {str(e)}")

    def export_to_excel(self):
        """تصدير بيانات الفواتير إلى Excel"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "الفواتير.csv", "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة'])

                    # كتابة البيانات
                    for invoice in invoices:
                        date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
                        client_name = invoice.client.name if invoice.client else ""
                        remaining = invoice.total_amount - invoice.paid_amount

                        status_names = {
                            'pending': 'معلقة',
                            'partially_paid': 'مدفوعة جزئياً',
                            'paid': 'مدفوعة بالكامل'
                        }
                        status_text = status_names.get(invoice.status, invoice.status or 'معلقة')

                        writer.writerow([
                            invoice.invoice_number or f"INV-{invoice.id}",
                            client_name,
                            date_str,
                            invoice.total_amount,
                            invoice.paid_amount,
                            remaining,
                            status_text
                        ])

                show_info_message("تم", f"تم تصدير الفواتير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات الفواتير إلى CSV"""
        try:
            import csv

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الفواتير.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.invoices_table.columnCount()):
                headers.append(self.invoices_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.invoices_table.rowCount()):
                row_data = []
                for col in range(self.invoices_table.columnCount()):
                    item = self.invoices_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")







    def show_statistics(self):
        """عرض إحصائيات الفواتير"""
        try:
            invoices = self.session.query(Invoice).all()

            if not invoices:
                show_info_message("إحصائيات الفواتير", "لا توجد فواتير لعرض الإحصائيات")
                return

            # حساب الإحصائيات العامة
            total_invoices = len(invoices)
            total_amount = sum(invoice.total_amount or 0 for invoice in invoices)
            total_paid = sum(invoice.paid_amount or 0 for invoice in invoices)
            total_remaining = total_amount - total_paid
            avg_invoice = total_amount / total_invoices if total_invoices > 0 else 0

            # إحصائيات حسب الحالة
            status_stats = {'pending': {'count': 0, 'amount': 0}, 'partially_paid': {'count': 0, 'amount': 0}, 'paid': {'count': 0, 'amount': 0}}
            for invoice in invoices:
                status = invoice.status or 'pending'
                if status in status_stats:
                    status_stats[status]['count'] += 1
                    status_stats[status]['amount'] += invoice.total_amount or 0

            # إحصائيات حسب العملاء
            client_stats = {}
            for invoice in invoices:
                if invoice.client:
                    client_name = invoice.client.name
                    if client_name not in client_stats:
                        client_stats[client_name] = {'count': 0, 'amount': 0}
                    client_stats[client_name]['count'] += 1
                    client_stats[client_name]['amount'] += invoice.total_amount or 0

            # أكبر وأصغر فاتورة
            amounts = [invoice.total_amount or 0 for invoice in invoices]
            max_amount = max(amounts) if amounts else 0
            min_amount = min(amounts) if amounts else 0

            # إنشاء محتوى الإحصائيات
            stats_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📊 إحصائيات الفواتير
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الإحصائيات: {QDate.currentDate().toString('yyyy-MM-dd')}

📈 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
🧾 إجمالي الفواتير: {total_invoices}
💰 إجمالي المبالغ: {int(total_amount):,} جنيه
💳 إجمالي المدفوع: {int(total_paid):,} جنيه
⚠️ إجمالي المتبقي: {int(total_remaining):,} جنيه
📊 متوسط الفاتورة: {int(avg_invoice):,} جنيه
🔺 أكبر مبلغ: {int(max_amount):,} جنيه
🔻 أصغر مبلغ: {int(min_amount):,} جنيه

📋 إحصائيات حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            status_names = {'pending': '⏳ معلقة', 'partially_paid': '🔄 مدفوعة جزئياً', 'paid': '✅ مدفوعة بالكامل'}
            for status, data in status_stats.items():
                if data['count'] > 0:
                    percentage = (data['count'] / total_invoices) * 100
                    stats_content += f"• {status_names.get(status, status)}:\n"
                    stats_content += f"  📝 العدد: {data['count']} فاتورة ({percentage:.1f}%)\n"
                    stats_content += f"  💰 المبلغ: {int(data['amount']):,} جنيه\n\n"

            if client_stats:
                stats_content += """
👥 أفضل العملاء (حسب المبلغ):
─────────────────────────────────────────────────────────────────────────────
"""
                top_clients = sorted(client_stats.items(), key=lambda x: x[1]['amount'], reverse=True)[:5]
                for client_name, data in top_clients:
                    stats_content += f"• {client_name}: {int(data['amount']):,} جنيه ({data['count']} فاتورة)\n"

            # عرض الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات الفواتير")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(stats_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.5;
                    background-color: #e2e8f0;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            close_button = StyledButton("❌ إغلاق", "secondary", "normal")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button.button)

            dialog.setLayout(layout)
            dialog.exec_()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def style_simple_button(self, button, button_type):
        """تطبيق تصميم مبسط على الأزرار"""
        try:
            colors = {
                'primary': '#3b82f6',
                'emerald': '#10b981',
                'danger': '#ef4444',
                'info': '#0ea5e9',
                'modern_teal': '#14b8a6'
            }

            color = colors.get(button_type, '#6b7280')
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception:
            pass

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الحالات", None),
            ("🟡 قيد الانتظار", "pending"),
            ("🟢 مدفوعة", "paid"),
            ("🔵 مدفوعة جزئيًا", "partially_paid"),
            ("🔴 ملغاة", "cancelled")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_invoices()